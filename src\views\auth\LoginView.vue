<template>
    <div class="main">
        <div class="wrap">
            <div class="container">
                <div class="left">
                    <BrandTitle />
                    <ThemePic />
                    <Footer />
                </div>
                <div class="right">
                    <BrandTitle />
                    <Forms />
                    <Footer />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onBeforeMount, onMounted } from 'vue'
import { BrandTitle, Footer, ThemePic, Forms } from './components'
import { getItem } from '@/utils/storage'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import systemService from '@/service/systemService'
import { setPageTitle } from '@/utils/system'

const route = useRoute()
const router = useRouter()
const store = useStore<RootState>()

const logout = async () => {
    store.dispatch('auth/logout')
}

const checkLogin = () => {
    const accessToken = getItem('access_token')
    if (accessToken) return router.push('/')
    logout()
}

onBeforeMount(() => {
    let oemkey = route.redirectedFrom?.query.c || route.query.c || JSON.parse(sessionStorage.getItem('oemkey') as string) || ''
    if(route.redirectedFrom?.query.c || route.query.c){
        sessionStorage.setItem('c-oemkey',JSON.stringify(oemkey))
    }
    systemService.systemOEMDetail({key:oemkey as string,productType:0}).then((res) => {
        // console.log('oemInfo1',res.data)
        if(res.success && res.data) {
            if(res.data.modules.length > 0){
                sessionStorage.setItem('ym-oemkey',JSON.stringify(res.data.key))
                sessionStorage.setItem('oemConfig', JSON.stringify(res.data))
                if(res.data.modules[0].config.webPageTabTitle){
                    setPageTitle(route.meta?.title as string, res.data.modules[0].config.webPageTabTitle)
                }
            }
            store.dispatch('auth/setOemConfig', res.data)
        }
    })
    let oemConfig = JSON.parse(sessionStorage.getItem('oemConfig') as string)
    if(oemConfig && oemConfig.modules.length > 0 && oemConfig.modules[0].config.webPageTabTitle){
        setPageTitle(route.meta?.title as string, oemConfig.modules[0].config.webPageTabTitle)
    }
})

onMounted(() => {
    checkLogin()
})
</script>

<style lang="scss" scoped>
.main {
    height: 100vh;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
}

.wrap {
    max-width: 1920px;
    height: 100%;
    margin: 0 auto;
    position: relative;
}

.container {
    max-width: 1320px;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    height: 100vh;
    justify-content: space-between;
}

.left,
.right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.right {
    align-items: flex-end;
}

.right {
    justify-content: center;
}

.right .brand-title {
    display: none;
}

.right .footer {
    display: none;
}

/* 自适应宽度 */
@media screen and (max-width: 1600px) {
    .container {
        max-width: 1098px;
    }
}

@media screen and (max-width: 1200px) {
    .container {
        max-width: 822px;
    }
}

@media screen and (max-width: 992px) {
    .container {
        max-width: 675px;
    }
}

@media screen and (max-width: 768px) {
    .container {
        max-width: 522px;
    }
}

@media screen and (max-width: 576px) {
    .container {
        max-width: 395px;
    }
}
</style>
