<script lang="ts" setup>
import moment from 'moment'
import { reactive, ref, onMounted, provide, nextTick, computed } from 'vue'

import { useStore } from 'vuex'

import type { Ref } from 'vue'

import crmService from '@/service/crmService'

import type {
    CrmGoodsPolicyListParams,
    CrmGoodsPolicyListItem,
    CrmGoodsPolicyEnumDataResponse,
    IGoodsPolicyItem,
} from '@/types/lead'

import { ElMessage, ElMessageBox } from 'element-plus'

import MatchRules from '@/components/match-rules/Index.vue'

import PolicyEdit from './components/PolicyEdit.vue'

import MatchCompany from '@/components/match-rules/MatchCompany.vue'
import CrmPolicyMatchDrawer from '@/components/crm/crm-policy-match/CrmPolicyMatchDrawer.vue'

provide('isDetail', true)
const pageInfo = reactive({ page: 1, pageSize: 20, total: 0 })

const tableData: Ref<CrmGoodsPolicyListItem[]> = ref([] as CrmGoodsPolicyListItem[])

const tableLoading: Ref<boolean> = ref(false)

const searchParams: Ref<CrmGoodsPolicyListParams> = ref({})

const store = useStore()
const isAdmin = computed(() => {
    let user = store.state.user?.account?.user || []
    if (user.role.includes('admin') || user.role.includes('yunwei')) {
        return true
    } else {
        return false
    }
})

const updateSearchParams = (params: CrmGoodsPolicyListParams) => {
    searchParams.value = params
    search()
}

const search = () => {
    crmService
        .crmGoodsPolicyList({
            ...searchParams.value,
            page: pageInfo.page,
            pageSize: pageInfo.pageSize,
        })
        .then((res) => {
            tableData.value = res.data
            pageInfo.total = res.total
        })
}

const showMatchDialog: Ref<boolean> = ref(false)

const ckRow: Ref<IGoodsPolicyItem> = ref({} as IGoodsPolicyItem)
const setRules = (row: CrmGoodsPolicyListItem) => {
    showMatchDialog.value = true
    ckRow.value = JSON.parse(JSON.stringify(row))
}
const matchRulesRef = ref()
const editRules = () => {
    // console.log(ckRow.value)
    let rules = matchRulesRef.value.exportRules()
    if (!rules || !ckRow.value.id) {
        return
    }
    crmService
        .crmGoodsPolicyRuleUpdate({
            id: ckRow.value.id,
            rules: {
                list: [rules],
            },
        })
        .then(() => {
            ElMessage({
                type: 'success',
                message: '保存成功',
            })

            showMatchDialog.value = false
            search()
        })
        .catch((err) => {
            console.log('err:', err)
        })
        .finally(() => {})
}

let crmGoodsPolicyEnumData: Ref<CrmGoodsPolicyEnumDataResponse> = ref({} as CrmGoodsPolicyEnumDataResponse)

provide('crmGoodsPolicyEnumData', crmGoodsPolicyEnumData)
const getCrmGoodsPolicyEnumData = () => {
    crmService.crmGoodsPolicyEnumData().then((res) => {
        console.log(res)
        crmGoodsPolicyEnumData.value = res
    })
}

const deletePolicy = (item: CrmGoodsPolicyListItem) => {
    ElMessageBox.confirm('是否确认删除', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            crmService.crmGoodsPolicyDelete(item.id).then(() => {
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
                search()
            })
        })
        .catch(() => {})
}

const batchDel = () => {
    ElMessageBox.confirm('是否确认删除', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            let ids = selectPolicys.value
                .map((item) => {
                    return item.id
                })
                .filter((id) => id !== undefined) as string[]
            crmService.crmGoodsPolicyDeleteBatch({ ids }).then(() => {
                ElMessage({
                    type: 'success',
                    message: '删除成功',
                })
                selectPolicys.value = []
                clearAllSelected()
                search()
            })
        })
        .catch(() => {})
}

const showDialog: Ref<boolean> = ref(false)
const handleEdit = (item: IGoodsPolicyItem) => {
    ckRow.value = item
    showDialog.value = true
}

const detaiVisable: Ref<boolean> = ref(false)

const handleDetail = (item: IGoodsPolicyItem) => {
    ckRow.value = item
    detaiVisable.value = true
}

const handleAdd = () => {
    ckRow.value = {} as IGoodsPolicyItem
    showDialog.value = true
}

const matchCompany = ref()
const seeMatchCompany = (item: IGoodsPolicyItem) => {
    ckRow.value = item
    nextTick(() => {
        matchCompany.value.seeMatchCompany()
    })
}

const selectPolicys: Ref<IGoodsPolicyItem[]> = ref([])

const handleSelectionChange = (val: IGoodsPolicyItem[]) => {
    console.log(val)
    selectPolicys.value = val
}


const tableListRef = ref()
const clearAllSelected = () => {
    if (tableListRef.value) {
        tableListRef.value.clearSelection()
    }
}

onMounted(() => {
    search()
    getCrmGoodsPolicyEnumData()
})
</script>
<template>
    <div ref="mainContentRef" class="height-100 display-flex flex-column">
        <!-- 搜索栏 -->
        <div ref="searchContentRef" class="b-margin-16 back-color-white">
            <div class="all-padding-16">
                <SearchBox
                    :searchOptionKey="'POLICY_SEARCH_OPTIONS'"
                    :customConfig="crmGoodsPolicyEnumData"
                    @updateSearchParams="updateSearchParams"
                ></SearchBox>
            </div>
        </div>
        <!-- 表格栏 -->
        <div class="all-padding-16 back-color-white border-box flex-grow-1">
            <!-- 工具条 -->
            <div ref="actionBarContentRef" class="display-flex top-bottom-center action-bar justify-end b-margin-16">
                <el-button type="primary" @click="handleAdd()">新增</el-button>

                <el-button type="danger" v-role="['admin', 'yunwei']" @click="batchDel()">批量删除</el-button>
            </div>
            <!-- 表格 -->
            <el-table
                ref="tableListRef"
                :data="tableData"
                v-loading="tableLoading"
                row-key="id"
                header-row-class-name="tableHeader"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="55" reserve-selection />
                <el-table-column label="政策标题" prop="name">
                    <template #default="scope">
                        <span class="pointer color-blue" @click="handleDetail(scope.row)">{{ scope.row.name }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="政策级别" prop="spu.policyLevelStr"></el-table-column>
                <el-table-column label="行业类别" prop="spu.policyIndustryStr"></el-table-column>
                <el-table-column label="发文部门" prop="spu.issuingDepartmentStr"></el-table-column>
                <el-table-column label="主题分类" prop="spu.policyTopicStr"></el-table-column>
                <el-table-column label="发文时间" prop="issuingTime">
                    <template #default="scope">
                        {{ scope.row.spu.issuingTime ? moment(scope.row.spu.issuingTime).format('YYYY-MM-DD') : '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="截止日期" prop="deadlineTime">
                    <template #default="scope">
                        {{ scope.row.spu.deadlineTime ? moment(scope.row.spu.deadlineTime).format('YYYY-MM-DD') : '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="剩余时间" prop="remainingDays">
                    <template #default="scope">{{ scope.row.remainingDays || 0 }}天</template>
                </el-table-column>
                <el-table-column label="设定条件">
                    <template #default="scope">
                        <span :class="scope.row.condition === 1 ? 'isCondition' : ''">
                            {{ scope.row.condition === 1 ? '已设定' : '未设定' }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="left" min-width="100px">
                    <template #default="scope">
                        <el-button
                            type="primary"
                            text
                            @click="handleDetail(scope.row)"
                            style="font-size: 16px; padding-left: 0"
                        >
                            详情
                        </el-button>
                        <el-dropdown>
                            <el-button type="primary" text>更多</el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item v-if="scope.row.allowOperator === 1">
                                        <el-button type="primary" text @click="handleEdit(scope.row)">编辑</el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item v-if="scope.row.allowOperator === 1">
                                        <el-button type="primary" text @click="setRules(scope.row)">设定规则</el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item>
                                        <el-button type="primary" text @click="seeMatchCompany(scope.row)">
                                            查看匹配企业
                                        </el-button>
                                    </el-dropdown-item>
                                    <el-dropdown-item v-if="scope.row.allowOperator === 1 || isAdmin">
                                        <el-button type="danger" text @click="deletePolicy(scope.row)">删除</el-button>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
        </div>
        <!-- <el-affix position="bottom" :offset="0"> -->
        <div class="pagination-bar back-color-white display-flex justify-end b-padding-6 r-padding-16">
            <el-pagination
                v-model:currentPage="pageInfo.page"
                v-model:page-size="pageInfo.pageSize"
                :total="pageInfo.total"
                :page-sizes="[20, 40, 60, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @change="search"
            />
        </div>
        <!-- </el-affix> -->
    </div>
    <el-dialog
        v-model="showMatchDialog"
        v-if="showMatchDialog"
        title="匹配规则"
        width="1060px"
        @close="showMatchDialog = false"
    >
        <MatchRules v-model:rules="ckRow.rules.list" ref="matchRulesRef" />

        <div class="display-flex justify-end">
            <el-button @click="showMatchDialog = false" type="primary">取消</el-button>
            <el-button @click="editRules" type="primary">保存</el-button>
        </div>
    </el-dialog>

    <PolicyEdit v-model:showDialog="showDialog" v-if="showDialog" @refreshData="search" :policyItem="ckRow" />

    <MatchCompany ref="matchCompany" :goodsId="ckRow.id" :type="'policy'" />

    <CrmPolicyMatchDrawer v-if="detaiVisable" v-model:visible="detaiVisable" :detailInfo="ckRow" :isDetail="true" />
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';

.isCondition {
    color: var(--main-orange-);
}
</style>
