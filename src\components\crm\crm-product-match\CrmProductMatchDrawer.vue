<script lang="ts" setup>
import {
    ref,
    // inject, 
    watch, provide
} from 'vue'
// import { ElMessage, ElMessageBox } from 'element-plus'
// import crmService from '@/service/crmService'
// import type { Ref } from 'vue'
import type {
    // ILeadData, 
    IGoodsProductItem
} from '@/types/lead'
import MatchRules from '@/components/match-rules/Index.vue'
const props = defineProps<{
    visible: boolean
    detailInfo: IGoodsProductItem
    isDetail?: boolean
}>()


provide('isDetail', props.isDetail)

console.log('props', props.detailInfo)
const drawerVisible = ref(props.visible)
watch(
    () => props.visible,
    (newVal) => {
        drawerVisible.value = newVal
    }
)
// const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

const emit = defineEmits(['update:visible'])
const handleClose = () => {
    emit('update:visible', false)
}
const tabList = [
    {
        id: 0,
        label: '产品大纲',
    },
    {
        id: 1,
        label: '返佣说明',
    },
    // {
    //     id: 2,
    //     label: '准入区域',
    // },
    // {
    //     id: 3,
    //     label: '禁入行业',
    // },
    {
        id: 2,
        label: '对比详情',
    },
]
const chooseItemId = ref(0)
const handleClickTab = (id: number) => {
    // 滚动到指定的元素
    chooseItemId.value = id
    const ele = document.querySelectorAll('.policy-tab-item')
    const targetEle = ele[id]
    targetEle.scrollIntoView({
        behavior: 'smooth',
    })
}

// const handleTurnFws = () => {
//     console.log('转移给服务商', crmDetail.value)
//     ElMessageBox.confirm('确认将该线索转换给服务商吗?', '提示', {
//         confirmButtonText: '确认',
//         cancelButtonText: '取消',
//         type: 'warning',
//     }).then(() => {
//         if (!props.detailInfo.id) {
//             return
//         }
//         crmService.goodsPolicyTransfer({
//             leadId: crmDetail.value.id,
//             productId: props.detailInfo.id
//         }).then(() => {
//             ElMessage({
//                 type: 'success',
//                 message: '操作成功'
//             })
//         })
//     }).catch(() => {
//         console.log('点击了取消')
//     })
// }
</script>
<template>
    <el-drawer :title="props.detailInfo.name" v-model="drawerVisible" v-if="drawerVisible" size="70%"
               @close="handleClose">
        <!-- <el-button type="primary" @click="handleTurnFws">转给服务商</el-button> -->
        <div class="display-flex top-bottom-center t-margin-16 b-margin-16">
            <div class="font-16 r-margin-24">授信额度范围：<span class="font-28 font-weight-500">{{
                props.detailInfo.spu?.moneyLimits || '-' }}</span></div>
            <el-tag v-show="props.detailInfo.sellPoint" type="warning" effect="plain">{{ props.detailInfo.sellPoint
            }}</el-tag>
        </div>
        <div class="item-tab">
            <div v-for="(item, index) in tabList" :key="index" class="item-tab-li pointer"
                 :class="{ active: chooseItemId === item.id }" @click="handleClickTab(item.id)">
                {{ item.label }}
            </div>
        </div>

        <!-- 产品大纲 -->
        <div class="policy-tab-item">
            <div class="display-flex top-bottom-center">
                <Icon class="r-margin-5" icon="icon-a-huaban2711" :size="16" color="var(--main-blue-)" />
                <div>产品大纲</div>
            </div>
            <div class="w-300 lr-padding-24 tb-margin-16">
                <div class="display-flex top-bottom-center space-between w-300 b-margin-16">
                    <div class="color-three-grey font-16">授信额度</div>
                    <div class="font-16">{{ props.detailInfo.spu?.moneyLimits || '-' }}</div>
                </div>
                <div class="display-flex top-bottom-center space-between w-300 b-margin-16">
                    <div class="color-three-grey font-16">年化利率</div>
                    <div class="font-16">{{ props.detailInfo.spu?.rateDown || 0 }}-{{ props.detailInfo.spu?.rateUpper ||
                        0 }}%</div>
                </div>
                <div class="display-flex top-bottom-center space-between w-300 b-margin-16">
                    <div class="color-three-grey font-16">借款周期</div>
                    <div class="font-16">{{ props.detailInfo.spu?.loanCycle || '-' }}</div>
                </div>
                <div class="display-flex top-bottom-center space-between w-300 b-margin-16">
                    <div class="color-three-grey font-16">还款方式</div>
                    <div class="font-16">{{ props.detailInfo.spu?.refundWay || '-' }}</div>
                </div>
                <div class="display-flex top-bottom-center space-between w-300">
                    <div class="color-three-grey font-16">通过率</div>
                    <div class="font-16">
                        {{ props.detailInfo.spu.passingRate ? (props.detailInfo.spu.passingRate === 'middle' ? '中' : '高') : '-' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- 返佣说明 -->
        <div class="policy-tab-item">
            <div class="display-flex top-bottom-center">
                <Icon class="r-margin-5" icon="icon-a-huaban87" :size="16" color="var(--main-blue-)"></Icon>
                <div>返佣说明</div>
            </div>
            <div class="l-padding-24 color-three-grey font-16 tb-margin-16">
                {{ props.detailInfo.spu?.rebateExplanation || '-' }}
            </div>
        </div>

        <!-- 准入区域 -->
        <!-- <div class="policy-tab-item">
            <div class="display-flex top-bottom-center">
                <Icon class="r-margin-5" icon="icon-a-huaban24" :size="16" color="var(--main-blue-)"></Icon>
                <div>准入区域</div>
            </div>
            <div class="lr-margin-24 font-16 tb-margin-16">
                <div class="display-flex space-between b-margin-10">
                    <div class="w-160 color-three-grey">全准入区域</div>
                    <div class="flex-1" v-if="props.detailInfo.sellAreaNames?.all?.length">
                        <span v-for='(pro, proIdx) in props.detailInfo.sellAreaNames.all' :key="proIdx">{{ pro }}<span
                            v-show='proIdx != props.detailInfo.sellAreaNames.all.length - 1'>/</span></span>
                    </div>
                    <div class="flex-1 color-three-grey" v-else>
                        无
                    </div>
                </div>
                <div class="display-flex space-between b-margin-10"
                     v-for="(city, index) in props.detailInfo.sellAreaNames?.cities || []" :key="index">
                    <div class="w-160 color-three-grey">{{ city[0] }}</div>
                    <div class="flex-1">
                        <span v-for="(c, cIdx) in city[1]" :key="cIdx">{{ c }}<span
                            v-show='cIdx != city[1].length - 1'>、</span></span>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- 禁入行业 -->
        <!-- <div class="policy-tab-item">
            <div class="display-flex top-bottom-center">
                <Icon class="r-margin-5" icon="icon-a-huaban72" :size="16" color="var(--main-red-)"></Icon>
                <div>禁入行业</div>
            </div>
            <div class="tb-margin-16 display-flex" v-if="props.detailInfo?.notSellIndustryNames?.length">
                <div class="border-radius-4 tb-padding-8 lr-padding-16 font-14 r-margin-16 b-margin-16"
                     style="background-color: #F5F7FA;" v-for="(industry, i) in props.detailInfo.notSellIndustryNames"
                     :key="i">{{ industry }}</div>
            </div>
            <div class="l-padding-24 color-three-grey font-16 tb-margin-16"
                 v-show='!props.detailInfo?.notSellIndustryNames?.length'>
                无禁入行业
            </div>
        </div> -->

        <!-- 对比详情 -->
        <div class="policy-tab-item">
            <div class="display-flex top-bottom-center b-margin-16">
                <Icon class="r-margin-5" icon="icon-a-huaban273" :size="16" color="var(--main-blue-)"></Icon>
                <div>对比详情</div>
            </div>
            <MatchRules v-if="detailInfo.matchScore?.rules" :read-only="true" :rules="detailInfo.matchScore.rules.list"
                        :scoreInfo="detailInfo.matchScore
                        " />
            <MatchRules v-else :read-only="true" :rules="detailInfo.rules.list" :scoreInfo="detailInfo.matchScore
            " />
        </div>
    </el-drawer>
</template>
<style scoped lang="scss">
.item-tab {
    width: 632px;
    height: 56px;
    border-radius: 8px;
    background: rgba(245, 247, 250, 1);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4px 4px 4px 4px;
    margin-bottom: 20px;

    .item-tab-li {
        width: 112px;
        height: 48px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 12px 24px 12px 24px;

        &:hover {
            background: #fff;
            box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
        }
    }
}

.active {
    background: #fff;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
}
</style>
