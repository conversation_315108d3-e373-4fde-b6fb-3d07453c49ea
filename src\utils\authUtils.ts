import { ElMessageBox, ElMessage } from 'element-plus'
import orderService from '@/service/orderService'
import collectService from '@/service/collectService'
import type { ILeadData } from '@/types/lead'

interface AuthUrlParams {
    socialCreditCode: string
    deductType: string
    companyId: string
    companyName: string
}

export interface AuthResult {
    url: string
    requestId: string
}

/**
 * 获取授权链接
 */
export const getAuthUrl = async (params: AuthUrlParams): Promise<AuthResult> => {
    const { socialCreditCode, deductType, companyId, companyName } = params

    const res = await collectService.getAuthUrl({
        socialCreditCode,
        deductType,
        companyId,
        companyName,
    })

    return {
        url: res.url,
        requestId: res.requestId,
    }
}

/**
 * 确认订单使用
 */
export const confirmOrderUsage = async (item: ILeadData, serviceKey: string) => {
    await ElMessageBox.confirm('扣除对应线索权益额度，是否确定使用？', '确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })

    const res = await orderService.orderServiceOrderPage({
        serviceKey,
        page: 1,
        pageSize: 99,
        status: 'NORMAL',
    })

    const validServices = res.data
        .filter((item) => item.remainingAmount > 0)
        .sort((a, b) => new Date(a.endTime).getTime() - new Date(b.endTime).getTime())

    const choseService = validServices[0]

    if (!choseService) {
        ElMessage({
            message: '可用额度不足，请充值！',
            type: 'warning',
        })
        throw new Error('可用额度不足，请充值！')
    }

    return await orderService.orderBuyLegal({
        orderId: choseService.serviceOrderId,
        serviceKey,
        socialCreditCode: item.socialCreditCode,
        companyName: item.companyName,
    })
}

/**
 * 确认报告生成
 */
export const confirmReportGeneration = async (item: ILeadData) => {
    if (item.isBuy) {
        return
    }

    await ElMessageBox.confirm('本次授权成功后,会扣除一份报告权益,是否确认生成报告链接?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
}

/**
 * 处理授权流程的通用方法
 */
export const handleAuthorization = async (
    item: ILeadData,
    serviceKey: string,
    deductType: string
): Promise<AuthResult> => {
    const { isBuy } = item || {}

    if (!isBuy) {
        await confirmOrderUsage(item, serviceKey)
    } else {
        await confirmReportGeneration(item)
    }

    return await getAuthUrl({
        socialCreditCode: item.socialCreditCode,
        deductType,
        companyId: item.companyId,
        companyName: item.companyName,
    })
}
