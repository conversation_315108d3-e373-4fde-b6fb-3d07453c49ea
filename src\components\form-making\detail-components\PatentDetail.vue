<template>
    <el-dialog
        v-model="dialogVisible"
        :title="'专利详情'"
        @close="handleClose()"
        width="60%" 
    >
        <div v-if="loading" class="text-center all-padding-24">
            <i class="el-icon-loading"></i> 正在加载专利信息...
        </div>
        <div
            v-else
            class="h-500 overflow-y-auto">
            <div class="width-100 flex">
                <div class='w-180 h-200 border'>
                    <img :src="patentDetail?.markImgOss || noData" alt="暂无图片" width="100%" height="100%">
                </div>
                <div class="l-margin-16 width-100">
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >专利名称</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >{{ patentDetail?.patentName || '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >专利类型</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >{{ patentDetail?.noticeType || '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >申请号</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{ patentDetail?.applyNo || '-' }}</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >申请日期</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{ patentDetail?.applyDate ? moment(patentDetail?.applyDate).format('YYYY-MM-DD') : '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >公开（公告）号</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{ patentDetail?.pubNo || '-' }}</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >公开（公告）日期</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{ patentDetail?.applyPubDate ? moment(patentDetail?.applyPubDate).format('YYYY-MM-DD') : '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >优先权号</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{ patentDetail?.priorityNo || '-' }}</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >优先权日</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{ patentDetail?.priorityDate ?  moment(Number(patentDetail?.priorityDate)).format('YYYY-MM-DD') : '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >发明人</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{patentDetail?.inventor || '-' }}</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >申请人</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{patentDetail?.applicant || '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >地址</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >{{patentDetail?.address || '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >代理机构</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >{{patentDetail?.agencyIns || '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >代理人</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{patentDetail?.agencyPerson || '-' }}</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >IPC分类号</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >{{patentDetail?.classifyNo || '-' }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >摘要</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >{{patentDetail?.summary || '-' }}</el-col>
                    </el-row>
                </div>
            </div>
            <div class="width-100 h-1 border tb-margin-16"></div>
            <div class="width-100">
                <span class="font-20">法律状态</span>
                <div class="width-100 t-margin-16" >
                    <el-row>
                        <el-col :span="8" class="all-padding-16 border bg-grey"
                        >法律状态公告日</el-col>
                        <el-col :span="8" class="all-padding-16 border bg-grey"
                        >法律状态</el-col>
                        <el-col :span="8" class="all-padding-16 border bg-grey"
                        >法律状态信息</el-col>
                    </el-row>
                    <div v-for="(item,index) in patentDetail!.patentLegalStatusDesc" :key="index">
                        <el-row>
                            <el-col :span="8" class="all-padding-16 border"
                            >{{ item.legalStatusDate ? moment(item.legalStatusDate).format('YYYY-MM-DD') : '-' }}</el-col>
                            <el-col :span="8" class="all-padding-16 border"
                            >{{item.legalStatusDesc}}</el-col>
                            <el-col :span="8" class="all-padding-16 border"
                            >{{item.legalStatusInfo}}</el-col>
                        </el-row>
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { onMounted, ref, watch, inject, getCurrentInstance} from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import type { IPatentDetailResponseItem } from '@/types/aic'
import noData from '@/assets/images/company/certificates-no-data.png'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))
const loading = ref(true)
const props = defineProps<{
    visible: boolean;
    row:IPatenetDetailParams;
}>()
const dialogVisible = ref<boolean>(props.visible)
interface IPatenetDetailParams {
    APPLYDATE: string
    APPLYNO: string
    APPLYPUBDATE: string
    APPLYPUBNO: string
    NOTICETYPE: string
    PATENTNAME: string
    lprs?: string
}
const patentDetail = ref<IPatentDetailResponseItem>()
const getDetail = (row:IPatenetDetailParams) => {
    aicService
        .getGsGetDetail({
            id: row.APPLYPUBNO,
            socialCreditCode: socialCreditCode.value,
            moduleName: 'patentInfo',
        }).then((res) => {
            const { errCode, data } = res
            if (errCode === 0) {
                patentDetail.value = data as IPatentDetailResponseItem
            }
        })
} 
watch(() => props.visible, async (val) => {
    dialogVisible.value = val
    getDetail(props.row)
    if (val) {
        setTimeout(() => {
            loading.value = false
        },1000)
    }
}, { immediate: true })
const emit = defineEmits(['update:visible'])
const handleClose = () => {
    dialogVisible.value = false
    loading.value = true
    emit('update:visible', false)
}

onMounted(() => {
})
</script>

<style lang='scss' scoped>

.bg-grey{
    background-color: #F5F7FA;
}
</style>