<template>
    <div
        style="text-align: right; margin-bottom: 16px; position: relative; z-index: 100"
    >
        <el-select v-model="selectYear" class="no-border w-150">
            <el-option
                v-for="item in ['2021', '2022', '2023', '2024', '2025']"
                :key="item"
                :value="item"
                :label="`${item}年`"
            ></el-option>
        </el-select>
    </div>
    <Fxlxbhqst
        :allTableData="allTableData"
        :riskTypeList="riskTypeList"
        :pieLoading="pieLoading"
        :selectYear="selectYear"
    />
</template>

<script lang='ts' setup>
import Fxlxbhqst from '@/views/risk-management/risk-alerts/components/Fxlxbhqst.vue'
import { ref, onMounted, inject } from 'vue'
import type { Ref } from 'vue'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'

const pieLoading = ref(false)
const selectYear = ref('2025')
const riskTypeList : Ref<IGetRiskTypeData[]> = inject('riskTypeList', ref([]))
const allTableData : Ref<IRiskEntListItem[]> = inject('allTableData', ref([]))

onMounted(async () => {

})
</script>

<style lang='scss' scoped>
</style>