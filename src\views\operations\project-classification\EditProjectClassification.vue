<template>
    <div style="background-color: #fff" class="search-model-category-detail">
        <view class="display-flex border-bottom">
            <view class="left border-right com-padding-16">
                <el-button type="primary" text @click="navBack">
                    <el-icon class="el-icon--right">
                        <ArrowLeft />
                    </el-icon>
                    返回
                </el-button>
            </view>
            <view class="right flex-1 com-padding-16 text-center">{{ categoryItem.name }}</view>
        </view>
        <view class="display-flex bottom">
            <view class="left border-right com-padding-16 ">
                <div>
                    <el-button type="primary" text @click="addNode">新增</el-button>
                </div>
                <div class="edit-box all-padding-16 l-margin-16 r-margin-16">
                    <div class="font-title b-margin-10">{{ categoryBaseInfo.id ? `编辑分类：${categoryBaseInfo.name}` : '新增分类' }}</div>
                    <div class="b-margin-10">
                        <el-input v-model="categoryBaseInfo.name" placeholder="分类名称" />
                    </div>
                    <div class="b-margin-10">
                        <el-input v-model="categoryBaseInfo.shortName" placeholder="分类简称" />
                    </div>
                    <div class="b-margin-10">
                        <el-select 
                            v-model="categoryBaseInfo.parentId" 
                            placeholder="上一级名称" 
                            filterable
                            :disabled="!categoryBaseInfo.parentId && !addFlag"
                            :filter-method="customFilterMethod"
                        >
                            <el-option 
                                v-for="item in filterList" 
                                :key="item.id" 
                                :label="item.name"
                                :value="item.id" />
                        </el-select>
                    </div>
                    <view class="b-margin-10 display-flex top-bottom-center justify-flex-end">
                        <el-button type="danger" v-if="categoryBaseInfo.id && categoryBaseInfo.parentId"
                                   @click="delNode">删除</el-button>
                        <el-button type="primary" @click="submit">提交</el-button>
                    </view>
                </div>
            </view>
            <view class="flex-1 com-padding-16 text-center" style="background-color: #FAFCFF;">
                <div v-loading="isLoading" element-loading-text="加载中..."></div>
                <TreeChart ref="treeChart" :key="componentKey" :chartData="detail" @nodeClick="nodeClick"/>
            </view>
        </view>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed, getCurrentInstance } from 'vue'
// import { useStore } from 'vuex'
// import type { RootState } from '@/types/store'
import type { CatagoryItem, CategoryItemResponse } from '@/types/aic'
import type { ISearchGetCategoryParams } from '@/types/company'
import TreeChart from './components/TreeEchart.vue'
import { useRoute, useRouter } from 'vue-router'
import aicService from '@/service/aicService'
import type { AddProjectParams } from '@/types/aic'
import { ElMessage,ElMessageBox } from 'element-plus'

// 图谱loading
const isLoading = ref(false)
const instance = getCurrentInstance()
const _lodash = instance?.appContext.config.globalProperties.$lodash
const CategoryId = ref('')
const route = useRoute()
const router = useRouter()
const addFlag = ref(true)
const treeChart = ref(null)
const detail = ref<CatagoryItem | null>(null)
const componentKey = ref(0)

const navBack = () => {
    router.back()
}

const categoryItem = ref<CatagoryItem>({
    id: '',
    name: '',
})

const categoryBaseInfo = ref<CatagoryItem>({
    id: '',
    name: '',
    shortName: '',
    parentId: '',
})

const reqParams = ref<AddProjectParams>({
    name: '',
    shortName: '',
    parentId: '',
})

const addNode = () => {
    addFlag.value = true
    let parentId = categoryBaseInfo.value.id
    categoryBaseInfo.value.id = ''
    // categoryBaseInfo.value = JSON.parse(JSON.stringify(categoryBaseInfo.svalue))
    categoryBaseInfo.value.name = ''
    categoryBaseInfo.value.shortName = ''
    categoryBaseInfo.value.parentId = parentId
}


const submit = () => {
    if(!categoryBaseInfo.value.name){
        ElMessage.error('分类名称不能为空')
    }
    // 没有id 就是新增, 有id 就是编辑
    else if (categoryBaseInfo.value.id){
        // 编辑
        reqParams.value.categoryId = categoryBaseInfo.value.id
        reqParams.value.name = categoryBaseInfo.value.name
        reqParams.value.shortName = categoryBaseInfo.value.shortName || ''
        reqParams.value.parentId = categoryBaseInfo.value.parentId || null
        aicService.modelUpdateCategory(reqParams.value).then((response) => {
            if(response.success) {
                ElMessage.success('编辑成功')
                isLoading.value = true
                setTimeout(() => {
                    isLoading.value = false
                    CategoryId.value = route.params.id as string
                    getDetail({categoryId: CategoryId.value}).then((res) => {
                        detail.value = JSON.parse(JSON.stringify(res.data[0]))
                    })
                    // 重新挂载图谱
                    componentKey.value += 1
                }, 1000)
            }
            else if(!response.success) {
                throw new Error(response.errMsg)
            }
            
            // 新增成功之后 置空
            categoryBaseInfo.value.name = ''
            categoryBaseInfo.value.shortName = ''
            categoryBaseInfo.value.parentId = ''

        }).catch(err => {
            ElMessage.error(err.message)
        })
    }else{
        // 新增
        if(!categoryBaseInfo.value.parentId){
            ElMessage.error('上一级名称不能为空')
        }else{
            reqParams.value.name = categoryBaseInfo.value.name
            reqParams.value.shortName = categoryBaseInfo.value.shortName || ''
            reqParams.value.parentId = categoryBaseInfo.value.parentId
            console.log('reqParams', reqParams.value)
            aicService.modelNewCategory(reqParams.value).then(() => {  
                ElMessage.success('新增成功')              
                CategoryId.value = route.params.id as string
                getDetail({categoryId: CategoryId.value}).then((res) => {
                    detail.value = JSON.parse(JSON.stringify(res.data[0]))
                })
                // 新增成功之后 置空
                categoryBaseInfo.value.name = ''
                categoryBaseInfo.value.shortName = ''
                categoryBaseInfo.value.parentId = ''
                componentKey.value += 1
            })
        }
    }
}

const delNode = () => {
    console.log('点击了删除',categoryBaseInfo.value.id)
    ElMessageBox.confirm('确定要删除该项目吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        aicService.modelDeleteCategory(categoryBaseInfo.value.id)
            .then((res) => {
                if(res.success){
                    ElMessage.success('删除成功')
                    isLoading.value = true
                    setTimeout(() => {
                        isLoading.value = false
                        getDetail({categoryId: CategoryId.value}).then((res) => {
                            detail.value = JSON.parse(JSON.stringify(res.data[0]))
                        })
                        componentKey.value += 1
                    }, 1000)
                    CategoryId.value = route.params.id as string
                }else if(!res.success){
                    ElMessage.error(res.errMsg)
                }
            }).catch(() => {
            })
        
        categoryBaseInfo.value = {
            id: '',
            name: '',
            shortName: '',
            parentId: ''
        }
    })
}

const getDetail = async (CategoryId:ISearchGetCategoryParams) => {
    return aicService.modelGetCategory(CategoryId) 
}

const nodeClick = (params: CategoryItemResponse) => {
    addFlag.value = false
    categoryBaseInfo.value = JSON.parse(JSON.stringify(params.data))
    console.log('点击了节点', categoryBaseInfo)
}

// const store = useStore<RootState>()

// const userInfo = computed(() => store.state.user.userInfo)
// const activeOrg = computed(() => store.state.app.activeOrg)
// const tenant = computed(() => store.state.app.tenant)

// 计算 allCategoryList
const categoryItem1 = computed(() => {return detail.value})

const allCategoryList = computed(() => {
    if (!categoryItem1.value) {
        return []
    }
    const extractNames = (data: CatagoryItem[]): CatagoryItem[] => {
        return _lodash.flatMap(data, (item: CatagoryItem) => [{ id: item.id, name: item.name }, ...(item.children ? extractNames(item.children) : [])])
    }
    return _lodash.uniqBy(extractNames([categoryItem1.value]), 'id')
})

// 通过filterList过滤categoryList,使搜索出来的数据准确展示
const filterList = ref<CatagoryItem[]>([])
const customFilterMethod = (query: string) => {
    // console.log('query',query)
    if(!query || query.trim() === ''){
        filterList.value = allCategoryList.value
        // console.log('filterList1111111',filterList.value)
        return allCategoryList.value
    }

    filterList.value = allCategoryList.value.filter((item: CatagoryItem) => item?.name?.includes(query))
    // console.log('filterList1111111',filterList.value)
}

onMounted(async () => {
    CategoryId.value = route.params.id as string
    const res = await getDetail({categoryId: CategoryId.value}) 
    // console.log('res1111111111', res)
    if (res.data && res.data.length > 0 ) {
        detail.value = JSON.parse(JSON.stringify(res.data[0]))
        categoryItem.value = JSON.parse(JSON.stringify(res.data[0]))
    }    
    filterList.value = allCategoryList.value
    // console.log('categoryItem1',categoryItem1.value)
    // console.log('allCategoryList',allCategoryList.value)
    // console.log('filterList',filterList.value)
})

</script>

<style lang='scss' scoped>
.search-model-category-detail {
    .left {
        min-width: 300px;
        width: 300px;
    }

    .com-padding-16{
    padding: .16rem;
    }

    .bottom {
        height: calc(100vh - 113px);
    }

    .edit-box {
        background-color: #F5F7FA;
    }
}

.font-title {
    font-size: 14px;
    font-weight: medium;
}
</style>