import http from '@/axios'
import type { ICommonResponse } from '@/types/axios'
import type { ICollectDownloadInvoiceParams, ICollectInvoiceListResponse, ICollectInvoiceParams } from '@/types/invoice'
import type {
    SearchCollectLogResponse,
    CollectLogParams,
    ExportParams,
    DownlodaReportParams,
    DownloadReportResponse,
    getIndicatorReportUrlParams,
    getIndicatorReportListResponse,
    getIndicatorReportUrlResponse
} from '@/types/report'

export default {
    collectPage(data: CollectLogParams): Promise<SearchCollectLogResponse> {
        return http.get(`/api/zhenqi-report/collect/page`, {
            params: data,
            hideError: true,
        })
    },

    collectExport(data: ExportParams): Promise<Blob> {
        return http.post('/api/zhenqi-report/collect/export', data, {
            responseType: 'blob',
            hideError: true,
        })
    },

    collectGetReportUrl(data: DownlodaReportParams): Promise<DownloadReportResponse> {
        const queryString = new URLSearchParams(data as unknown as Record<string, string>).toString()
        return http.get(`/api/zhenqi-report/report/get-report-url?${queryString}`, {
            hideError: true,
        })
    },
    collectGetInvoiceInfo(data: ICollectInvoiceParams): Promise<ICollectInvoiceListResponse> {
        return http.post('/api/zhenqi-report/collect/get-invoice-info', data, {
            hideError: true,
        })
    },
    collectDownloadInvoiceFile(data: ICollectDownloadInvoiceParams): Promise<Blob | ICommonResponse> {
        return http
            .post('/api/zhenqi-report/collect/download-invoice-file', data, {
                responseType: 'blob',
                hideError: true,
            })
            .then(async (res) => {
                if (res.type === 'application/json') {
                    const text = await res.text()
                    return JSON.parse(text) as ICommonResponse
                }
                return res
            })
    },
    // 获取指标平台报告列表
    getIndicatorReportList(): Promise<getIndicatorReportListResponse> {
        return http.get(`/api/zhenqi-report/report/get-indicator-report-list`, {
            hideError: true
        })
    },
    // 获取指标平台报告链接
    getIndicatorReportUrl(data: getIndicatorReportUrlParams): Promise<getIndicatorReportUrlResponse> {
        return http.get(`/api/zhenqi-report/report/get-indicator-report-url`, {
            params: data,
            hideError: true
        })
    },
}
