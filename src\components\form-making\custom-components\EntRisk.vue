<template>
    <FxgkTable :allTableData="allTableData" :riskTypeList="riskTypeList"></FxgkTable>
</template>

<script lang='ts' setup>
import { ref, inject } from 'vue'
import type { Ref } from 'vue'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'
import FxgkTable from '@/views/risk-management/risk-alerts/components/FxgkTable.vue'

const riskTypeList : Ref<IGetRiskTypeData[]> = inject('riskTypeList', ref([]))
const allTableData : Ref<IRiskEntListItem[]> = inject('allTableData', ref([]))
</script>

<style lang='scss' scoped>

</style>

