// 富文本内容显示样式
// 用于支持v-html渲染的Quill富文本内容的样式显示

// 全局富文本内容样式类
.quill-content {
    line-height: 1.6;
    font-size: 14px;
    
    // Quill对齐样式
    .ql-align-center {
        text-align: center;
    }
    
    .ql-align-right {
        text-align: right;
    }
    
    .ql-align-left {
        text-align: left;
    }
    
    .ql-align-justify {
        text-align: justify;
    }
    
    // 文本格式样式
    strong {
        font-weight: bold;
    }
    
    em {
        font-style: italic;
    }
    
    u {
        text-decoration: underline;
    }
    
    s {
        text-decoration: line-through;
    }
    
    // 列表样式
    ol, ul {
        padding-left: 1.5em;
        margin: 0.5em 0;
    }
    
    ol li, ul li {
        margin: 0.2em 0;
    }
    
    // 引用样式
    blockquote {
        border-left: 4px solid #ccc;
        margin: 0.5em 0;
        padding-left: 16px;
        color: #666;
        font-style: italic;
    }
    
    // 标题样式
    h1, h2, h3, h4, h5, h6 {
        margin: 0.5em 0;
        font-weight: bold;
    }
    
    h1 { font-size: 2em; }
    h2 { font-size: 1.5em; }
    h3 { font-size: 1.17em; }
    h4 { font-size: 1em; }
    h5 { font-size: 0.83em; }
    h6 { font-size: 0.67em; }
    
    // 段落样式
    p {
        margin: 0.5em 0;
    }
    
    // 链接样式
    a {
        color: #1966ff;
        text-decoration: none;
        
        &:hover {
            text-decoration: underline;
        }
    }
    
    // 代码样式
    code {
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
    }
    
    pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        
        code {
            background: none;
            padding: 0;
        }
    }
}

// 为了向后兼容，也支持直接在元素上使用ql-editor类
.ql-editor {
    @extend .quill-content;
}
