<template>
    <div class="all-padding-16 width-100 model-info-box height-100" v-if="isLoading">
        <el-tabs v-model="activeCategory" stretch :tab-position="'left'" class="model-tabs">
            <el-tab-pane
                class="height-100"
                :label="category.title"
                :name="category.name"
                v-for="category in categoryDetailList"
                :key="category.name"
            >
                <div v-if="activeCategory === category.name" class="height-100 model-content">
                    <div class="model-box-fixed display-flex gap-16 flex-wrap model-box tb-padding-10">
                        <div
                            class="model-label text-nowrap border all-padding-8 border-radius-4 font-12 display-flex top-bottom-center left-right-center"
                            v-for="item in category.children"
                            :key="item.name"
                            @click="item.showType === 'list' || item.showType === 'custom' ? item.total === 0 ? '' : jumpHref(item.name) : jumpHref(item.name)"
                            :class="
                                item.showType === 'list' || item.showType === 'custom'
                                    ? item.total === 0
                                        ? 'not-allow-back not-allow'
                                        : 'pointer back-color-white '
                                    : 'pointer back-color-white '
                            "
                        >
                            {{ item.title }}
                            <span class="l-margin-5" v-show="item.total && item.total > 0">{{ item.total }} </span>
                        </div>
                    </div>
                    <div class="model-content-scrollable">
                        <div 
                            v-if="category.children.filter(item => !((item.showType === 'list' || item.showType === 'custom') && item.total === 0)).length === 0 "
                            class="flex-center flex-column"
                        >
                            <img class="w-250 h-200" :src="noData" alt="暂无数据" />
                            <div>暂无{{category.title}}</div>
                        </div>
                        <div
                            v-else
                            v-for="item in category.children.filter(item => !((item.showType === 'list' || item.showType === 'custom') && item.total === 0))"
                            :key="item.name"
                            :id="'model-' + item.name"
                            class="model-item t-margin-21"
                        >
                            <div class="model-title b-margin-17">{{ item.title }}</div>
                            <div v-if="item.showType === 'table'">
                                <SearchCompanyFmForm
                                    @updateBuyStatus="updateBuyStatus"
                                    :modelItem="item"
                                    :companyRelateItem="relationModel"
                                />
                            </div>
                            <div v-if="item.showType === 'list'">
                                <SearchCompanyFmTable
                                    @updateBuyStatus="updateBuyStatus"
                                    :modelItem="item"
                                    :companyRelateItem="relationModel"
                                    @updateTotal="
                                        (total) => {
                                            updateModelTotal(total, item)
                                        }
                                    "
                                />
                            </div>
                            <div v-if="item.showType === 'merge'">
                                <SearchCompanyFmMerge @updateBuyStatus="updateBuyStatus" :modelItem="item" />
                            </div>
                            <div v-if="item.showType === 'custom'">
                                <!-- 锁定状态显示 -->
                                <div v-if="componentLockStatus[item.name]" class="no-pay-item display-flex width-100 border-radius-8 oh" style="height: 400px">
                                    <div style="margin: auto" class="display-flex flex-column">
                                        <div style="text-align: center; margin-bottom: 5px">
                                            <el-icon size="50" color="#409eff">
                                                <Lock />
                                            </el-icon>
                                        </div>
                                        <el-button size="large" type="primary" @click="handleUnlock()"> 点击查看 </el-button>
                                    </div>
                                </div>
                                <!-- 正常内容显示 -->
                                <component
                                    v-else
                                    :is="componentsMap[item.name]"
                                    :modelItem="item"
                                    @updateTotal="
                                        (total: number) => {
                                            updateModelTotal(total, item)
                                        }
                                    "
                                    @updateLockStatus="
                                        (isLocked: boolean) => {
                                            updateComponentLockStatus(isLocked, item)
                                        }
                                    "
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
    <div v-else>
        <el-skeleton :rows="5" animated />
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, provide, inject } from 'vue'
import type { Ref } from 'vue'
import type { IGetModelCategoryResponse } from '@/types/company'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'
import aicService from '@/service/aicService'
import eventBus from '@/utils/eventBus'
import SearchCompanyFmForm from '@/components/form-making/SearchCompanyFmGenerateForm.vue'
import SearchCompanyFmTable from '@/components/form-making/SearchCompanyFmGenerateTable.vue'
import SearchCompanyFmMerge from '@/components/form-making/SearchCompanyFmGenerateMerge.vue'
import customComponents from '@/components/form-making/custom-components'
import indicatorService from '@/service/indicatorService'
import orderService from '@/service/orderService'
import { ElMessageBox, ElMessage } from 'element-plus'
import noData from '@/assets/images/no-data.png'

const componentsMap = computed(() => {
    return { ...customComponents } as Record<string, unknown>
})

const props = defineProps<{
    socialCreditCode: string
}>()

const isLoading: Ref<boolean> = ref(false)

// 注入公司名称，用于解锁操作
const companyName: Ref<string> = inject('companyName', ref(''))

const activeCategory: Ref<string> = ref('')

const categoryDetailList: Ref<IGetModelCategoryResponse[]> = ref([])

const updateModelTotal = (total: number, item: IGetModelCategoryResponse) => {
    item.total = total
    categoryDetailList.value.forEach(category => {
        category.children.forEach(childItem => {
            if (childItem.name === item.name) {
                childItem.total = total
            }
        })
    })
    console.log('categoryDetailList.value',categoryDetailList.value)
}

// 管理每个组件的锁定状态
const componentLockStatus: Ref<Record<string, boolean>> = ref({})

const updateComponentLockStatus = (isLocked: boolean, item: IGetModelCategoryResponse) => {
    componentLockStatus.value[item.name] = isLocked
}

const handleUnlock = () => {
    ElMessageBox.confirm('扣除对应线索权益额度，是否确定使用?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            orderService
                .orderBuyLegal({
                    socialCreditCode: props.socialCreditCode,
                    companyName: companyName.value,
                    serviceKey: 'xs',
                })
                .then(() => {
                    // 解锁所有模块，将所有模块的锁定状态都设置为 false
                    categoryDetailList.value.forEach(category => {
                        category.children.forEach(childItem => {
                            componentLockStatus.value[childItem.name] = false
                        })
                    })
                    ElMessage({
                        message: '使用成功',
                        type: 'success',
                    })
                    // 触发刷新
                    eventBus.$emit('refreshBuyStatus')
                })
        })
        .catch(() => {})
}
const jumpHref = (name: string) => {
    // 找到可滚动的容器
    const scrollContainer = document.querySelector('.model-content-scrollable')
    const targetElement = document.getElementById(`model-${name}`)

    if (scrollContainer && targetElement) {
        // 计算目标元素相对于滚动容器的位置
        const containerRect = scrollContainer.getBoundingClientRect()
        const targetRect = targetElement.getBoundingClientRect()
        const scrollTop = scrollContainer.scrollTop

        // 计算需要滚动的距离
        const targetScrollTop = scrollTop + (targetRect.top - containerRect.top) - 20 // 20px 的偏移量

        // 平滑滚动到目标位置
        scrollContainer.scrollTo({
            top: targetScrollTop,
            behavior: 'smooth',
        })
    }
}

const relationModel: Ref<IGetModelCategoryResponse> = ref({} as IGetModelCategoryResponse)
const getRelateModel = () => {
    aicService.gsGetGetAppointDetailModel({ name: 'CompanyRelation' }).then((res) => {
        relationModel.value = res.data
    })
}

const getModelList = () => {
    isLoading.value = false
    aicService.conditionGetDetailModel().then((res: IGetModelCategoryResponse[]) => {
        // res.forEach(item => {
        //     if(item.name === 'RiskInfo'){
        //         item.children.forEach((child)=>{
        //             if(['AnnTrialInfo', 'ChattelMortgageInfoCB', 'CourtAnnouncement', 'JudgementsInfo'].includes(child.name)){
        //                 child.showType = 'custom'
        //             }
        //         })
        //     }
        // })
        categoryDetailList.value = res
        if (!activeCategory.value) {
            activeCategory.value = res[0].name
        }
        isLoading.value = true
    })
}

const allTableData = ref<IRiskEntListItem[]>([])
const riskTypeList = ref<IGetRiskTypeData[]>([])
provide('allTableData', allTableData)
provide('riskTypeList', riskTypeList)
const getRiskEntList = async () => {
    await indicatorService
        .getRiskEntList({
            socialCreditCode: props.socialCreditCode,
        })
        .then((res) => {
            console.log('企业风险列表', res)
            const { success, data } = res
            if (success) {
                allTableData.value = data
            }
        })
}
const getRiskTypeData = () => {
    indicatorService
        .getRiskTypeData({
            socialCreditCode: props.socialCreditCode,
        })
        .then((res) => {
            riskTypeList.value = Object.entries(res).map(([name, value]) => ({
                name,
                value,
            }))
        })
        .finally(() => {
        })
}

const updateBuyStatus = () => {
    categoryDetailList.value.forEach(category => {
        category.children.forEach(childItem => {
            componentLockStatus.value[childItem.name] = false
        })
    })
    eventBus.$emit('refreshBuyStatus')
}

onUnmounted(() => {
    eventBus.$off('refreshBuyStatus', () => {})
})

onMounted(async () => {
    await getRiskTypeData()
    await getRiskEntList()
    await getRelateModel()
    await getModelList()
    eventBus.$on('refreshBuyStatus', () => {
        getModelList()
    })
})
defineExpose({
    getModelList,
})
</script>

<style lang="scss" scoped>
.model-label {
    width: 135px;
}

.model-item {
    .model-title {
        border-left: 4px solid var(--main-blue-);
        padding-left: 8px;
    }
}

.model-info-box {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.model-tabs {
    height: 100%;
    display: flex;

    :deep(.el-tabs__content) {
        height: 100%;
        flex: 1;
        overflow: hidden;
    }

    :deep(.el-tab-pane) {
        height: 100%;
    }
}

.model-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.model-box-fixed {
    position: sticky;
    top: 0;
    z-index: 10;
    background: white;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 16px;
}

.model-box {
    background: linear-gradient(
        to right,
        rgba(235, 241, 255, 1) 0%,
        rgba(255, 255, 255, 1) 70.15%,
        rgba(255, 255, 255, 1) 100%
    );
}

.model-content-scrollable {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }
}

.not-allow-back {
    background-color: var(--second-blue);
}

.no-pay-item {
    background: url('@/assets/images/model-lock.jpg') no-repeat;
    width: 100%;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    border: 3px solid var(--border-color);
}
</style>
