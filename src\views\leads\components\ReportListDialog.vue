<script lang='ts' setup>
import { provide, ref, watch, computed } from 'vue'
import type { ILeadData } from '@/types/lead'
import CrmBaseInfoReport from '@/components/crm/crm-base-info/CrmBaseInfoReport.vue'
import OtherReportList from '@/views/leads/components/OtherReportList.vue'

const props = defineProps<{
    visible: boolean
    companyInfo?: ILeadData
    type?: string
    socialCreditCode?: string
    requestId?: string
}>()
const dialogVisible = ref(props.visible)
const crmDetail = computed(() => {
    return props.companyInfo
})
watch(() => props.visible, (newVal) => {
    dialogVisible.value=newVal
})

provide('crmDetail', crmDetail)
const emit = defineEmits(['close','update:visible'])
const handleClose = () => {
    emit('update:visible', false)
}
</script>
<template>
    <div>
        <el-drawer v-model="dialogVisible" direction="rtl" size="400" @close="handleClose">
            <template #header>
                <div class="title">{{ props?.type === 'other' ? '其他报告' : '相关报告' }} </div>
            </template>
            <template #default>
                <div v-if="props.type === 'other'">
                    <OtherReportList :span="24" :socialCreditCode="socialCreditCode" :requestId="requestId"></OtherReportList>
                </div>
                <div v-else>
                    <CrmBaseInfoReport :span="24"></CrmBaseInfoReport>
                </div>
            </template>
        </el-drawer>
    </div>
</template>
<style scoped lang='scss'>
</style>
