<template>
    <Fxlxfbt
        :allTableData="allTableData"
        :riskTypeList="riskTypeList"
        :pieLoading="pieLoading"
    ></Fxlxfbt>
</template>

<script lang='ts' setup>
import Fxlxfbt from '@/views/risk-management/risk-alerts/components/Fxlxfbt.vue'
import { ref, onMounted, inject } from 'vue'
import type { Ref } from 'vue'
import type { IGetRiskTypeData, IRiskEntListItem } from '@/types/indicator'

const pieLoading = ref(false)
const riskTypeList : Ref<IGetRiskTypeData[]> = inject('riskTypeList', ref([]))
const allTableData : Ref<IRiskEntListItem[]> = inject('allTableData', ref([]))

onMounted(async () => {

})

</script>

<style lang='scss' scoped>
</style>