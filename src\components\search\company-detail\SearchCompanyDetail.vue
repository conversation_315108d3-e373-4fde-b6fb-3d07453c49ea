<template>
    <div class="company-detail-box width-100 display-flex height-100">
        <!-- 外层滚动容器 -->
        <div class="outer-scroll-container flex-1">
            <div class="company-detail display-flex flex-column">
                <CompanyBaseInfo class="base-info-box" ref="baseInfo" @updateCompanyInfo="updateCompanyInfo" />
                <div class="model-info-container">
                    <CompanyModelInfo ref="modelList" :socialCreditCode="socialCreditCode"/>
                </div>
            </div>
        </div>
        <div class="contact-box oh lr-padding-24 tb-padding-12 back-color-white height-100">
            <ContactList ref="contactList" />
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, onUnmounted, provide, inject, nextTick } from 'vue'
import type { Ref } from 'vue'
import type { CompanyBaseInfo as CompanyInfoType } from '@/types/company'
import type { GsGetCompanyClueInfoResponse } from '@/types/lead'
import eventBus from '@/utils/eventBus'
import crmService from '@/service/crmService'
import orderService from '@/service/orderService'
import ContactList from '@/components/company/ContactList.vue'
import CompanyBaseInfo from './CompanyBaseInfo.vue'
import CompanyModelInfo from './CompanyModelInfo.vue'

const companyInfo = ref({} as CompanyInfoType)

const companyName: Ref<string> = ref('')

const buyStatus: Ref<boolean> = ref(false)

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))

const emits = defineEmits(['refreshList'])

const companyClueInfo: Ref<GsGetCompanyClueInfoResponse> = ref({} as GsGetCompanyClueInfoResponse)
provide('companyInfo', companyInfo)
provide('companyName', companyName)
provide('companyClueInfo', companyClueInfo)
provide('buyStatus', buyStatus)

// const contactList = ref<InstanceType<typeof ContactList> | null>(null)
const baseInfo = ref<InstanceType<typeof CompanyBaseInfo> | null>(null)
// const modelList = ref<InstanceType<typeof CompanyModelInfo> | null>(null)

// 基础信息高度
const baseInfoHeight = ref(0)

const updateCompanyInfo = (data: CompanyInfoType) => {
    companyInfo.value = data
    companyName.value = data.companyName

    gsGetCompanyBuyStatusInfo()

    // 获取基础信息高度
    nextTick(() => {
        if (baseInfo.value?.$el) {
            baseInfoHeight.value = baseInfo.value.$el.offsetHeight
            setupOuterScroll()
        }
    })
}

const setupOuterScroll = () => {
    const outerContainer = document.querySelector('.outer-scroll-container') as HTMLElement
    const companyDetail = document.querySelector('.company-detail') as HTMLElement

    if (outerContainer && companyDetail && baseInfoHeight.value > 0) {
        const totalHeight = outerContainer.clientHeight + baseInfoHeight.value
        companyDetail.style.height = `${totalHeight}px`
    }
}

const gsGetCompanyClueInfo = () => {
    crmService.gsGetCompanyClueInfo({ socialCreditCode: socialCreditCode.value }).then(getCrmUserRes => {
        companyClueInfo.value = getCrmUserRes.data
    })
}
const gsGetCompanyBuyStatusInfo = () => {
    orderService.orderCheckEntBuy({
        socialCreditCode: socialCreditCode.value,
        companyName: companyName.value,
        serviceKey: 'xs'
    }).then(getCompanyBuyStatusInfo => {

        if (getCompanyBuyStatusInfo.status === '1') {
            //已购买
            buyStatus.value = true
        }

        console.log(getCompanyBuyStatusInfo)
    })
}

onMounted(() => {
    gsGetCompanyClueInfo()
    eventBus.$on('refreshBuyStatus', () => {
        gsGetCompanyBuyStatusInfo()
        gsGetCompanyClueInfo()
        emits('refreshList')
    })

    // 监听窗口大小变化，重新计算滚动区域
    window.addEventListener('resize', setupOuterScroll)
})
onUnmounted(() => {
    eventBus.$off('refreshBuyStatus', () => { })
    window.removeEventListener('resize', setupOuterScroll)
})
</script>

<style lang='scss' scoped>
.company-detail-box {
    .outer-scroll-container {
        overflow-y: auto;
        height: 100%;
        max-width: calc(100% - 338px);

        &::-webkit-scrollbar {
            width: 0px;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
                background: #a8a8a8;
            }
        }
    }

    .company-detail {
        background: linear-gradient(to right,
                rgba(235, 241, 255, 1) 0%,
                rgba(255, 255, 255, 1) 70.15%,
                rgba(255, 255, 255, 1) 100%);
        border-right: 1px solid var(--border-color);
        min-height: 100%;
    }

    .base-info-box {
        flex-shrink: 0;
    }

    .model-info-container {
        flex: 1;
        min-height: 0;
    }

    .contact-box {
        max-width: 438px;
        min-width: 338px;
        width: 22%;
        height: 100%;
    }
}
</style>