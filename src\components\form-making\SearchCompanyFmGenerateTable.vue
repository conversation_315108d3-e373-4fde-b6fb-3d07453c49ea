<template>
    <div class="formmaking-table">
        <div v-if="isLock" class="no-pay-item display-flex width-100 border-radius-8 oh" style="height: 400px">
            <div style="margin: auto" class="display-flex flex-column">
                <div style="text-align: center; margin-bottom: 5px">
                    <el-icon size="50" color="#409eff">
                        <Lock />
                    </el-icon>
                </div>
                <el-button size="large" type="primary" @click="pullLock">点击查看</el-button>
            </div>
        </div>
        <el-table
            v-else
            v-loading="loading"
            ref="table"
            :data="modelData"
            tooltip-effect="dark"
            border
            table-layout="fixed"
            fit
            :header-cell-style="{
                background: '#ECF5FF',
            }"
            size="large"
            empty-text="暂无数据"
        >
            <!-- <el-table-column
                type="index"
                label="序号"
                width="80"
                align="center"
                :index="indexFilter"
                header-align="center"
            ></el-table-column> -->
            <template v-for="pro in jsonModelCurrentVersion" :key="pro.model">

                <el-table-column
                    v-if="pro.type == 'imgupload'"
                    align="center"
                    header-align="center"
                    :label="pro.name"
                    :key="pro"
                    :prop="pro.key"
                    :width="pro.width || ''"
                >
                    <template #default="scope">
                        
                        <el-image
                            style="width: 100%; height: 40px"
                            :src="scope.row[pro.model]"
                            :zoom-rate="1.2"
                            :max-scale="7"
                            :min-scale="0.2"
                            :prediv-src-list="[scope.row[pro.model]]"
                            :initial-index="0"
                            fit="contain"
                            :prediv-teleported="true"
                            :hide-on-click-modal="true"
                        >
                            <template #error>
                                <div class="image-slot">
                                    <el-icon><icon-picture /></el-icon>
                                </div>
                            </template>
                        </el-image>
                    </template>
                </el-table-column>

                <el-table-column
                    v-if="pro.type == 'input' || pro.type == 'time' || pro.type == 'date'"
                    align="center"
                    header-align="center"
                    :label="pro.name"
                    :key="pro"
                    :prop="pro.key"
                    :width="pro.width || ''"
                >
                    <template #default="scope">
                        <!-- 具体列表项可以点击 -->
                        <div v-if="pro.model === 'reportYear'">
                            <div class="pointer color-primary">
                                <span
                                    v-html="toLangStr(scope.row[pro.model], pro.type, pro).str"
                                    @click="handleAnnualReport(scope.row)"
                                ></span>
                            </div>
                        </div>
                        <div v-else-if="pro.model === 'PATENTNAME'">
                            <div class="pointer color-primary">
                                <el-tooltip :content='scope.row[pro.model]'>
                                    <span 
                                        v-html="scope.row[pro.model]"
                                        @click="getPatentDetail(scope.row)"    
                                    ></span>
                                </el-tooltip>
                            </div>
                        </div>
                        <div v-else-if="pro.model === 'projectName'">
                            <div class="pointer color-primary">
                                <el-tooltip :content='scope.row[pro.model]'>
                                    <span 
                                        v-html="scope.row[pro.model]"
                                        @click="toProjectUrl(scope.row)"    
                                    ></span>
                                </el-tooltip>
                            </div>
                        </div>
                        <el-tooltip
                            v-else-if="scope.row[pro.model] && scope.row[pro.model].length > 25 && pro?.options?.customClass !== 'url'"
                            effect="light"
                            popper-style="max-width:500px"
                        >
                            <template #content>
                                <span v-html="scope.row[pro.model]"></span>
                            </template>
                            <span v-html="toLangStr(scope.row[pro.model], pro.type, pro).str"></span>
                        </el-tooltip>
                        <span v-else class="font-14" v-html="toLangStr(scope.row[pro.model], pro.type, pro).str"></span>
                        <!-- <el-button
                            v-if="toLangStr(scope.row[pro.model], pro.type, pro).flag && pro.type == 'input'"
                            type="primary"
                            text
                            @click="((moreInfoStr = scope.row[pro.model]), (moreInfoDialogShow = true))"
                        >
                            更多
                        </el-button> -->
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="pro.type == 'group'"
                    :key="pro.key"
                    :label="pro.name"
                    align="center"
                    header-align="center"
                    :width="pro.width || ''"
                >
                    <template #default="scope">
                        <RelateTableColumn
                            :data="scope.row"
                            :channelType="channelType"
                            :pro="pro"
                            :modelName="modelItem.name"
                        />
                    </template>
                </el-table-column>
                <el-table-column 
                    v-if="pro.type == 'select'" 
                    align="center" 
                    header-align="center" 
                    :label="pro.name"
                    :width="pro.width || ''"
                >
                    <template #default="scope">
                        
                        <div>{{ scope.row[pro.model].join(', ') }}</div>
                        <!-- {{ scope.row[pro.model] }} -->
                        <!-- <el-button v-if="scope.row[pro.key] && scope.row[pro.key].length" @click="
                                ; (innerTableShow = true),
                            (innerTableData = scope.row[pro.key]),
                            (innerTableSchema = pro)
                            " type="primary" text>
                            点击查看
                        </el-button>
                        <text v-else>-</text> -->
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="pro.type == 'editor'"
                    :key="pro.model"
                    :label="pro.name"
                    align="center"
                    header-align="center"
                    :width="pro.width || ''"
                >
                    <template #default="scope">
                        
                        <div
                            class="pointer font-14"
                            style="color: #509de5"
                            @click="getCompanyDetail(scope.row[pro.model])"
                        >
                            {{ scope.row[pro.model] }}
                        </div>
                    </template>
                </el-table-column>
            </template>
        </el-table>
        <div class="display-flex top-bottom-center margin-top-20 justify-flex-end">
            <el-pagination
                :hide-on-single-page="true"
                v-model:currentPage="pageInfo.page"
                v-model:page-size="pageInfo.pageSize"
                layout="total, prev, pager, next"
                :total="pageInfo.total"
                @current-change="getModelData()"
            />
        </div>
        <el-dialog ref="moreInfoDialog" v-model="moreInfoDialogShow">
            <div class="margin-left-30 margin-right-30 vm" v-html="moreInfoStr"></div>
        </el-dialog>
    </div>
    <AnnualReport v-if="annualRow" v-model:visible="annualReportDialogVisible" :row="annualRow" />
    <PatentDetail v-if="patentRow" v-model:visible="patentDetailDialogVisible" :row="patentRow" />
</template>

<script lang="ts" setup>
import { ref, onMounted, inject, watch } from 'vue'
import { useRouter } from 'vue-router'
import type { Ref } from 'vue'
import type { IModelJson } from '@/types/model'
import type { IGetModelCategoryResponse } from '@/types/company'
import { ElMessageBox, ElMessage } from 'element-plus'
import aicService from '@/service/aicService'
import orderService from '@/service/orderService'
import RelateTableColumn from './custom-components/RelateTableColumn.vue'
import AnnualReport from './detail-components/AnnualReport.vue'
import PatentDetail from './detail-components/PatentDetail.vue'

const router = useRouter()

interface definetypes {
    modelItem: IGetModelCategoryResponse
    companyRelateItem?: IGetModelCategoryResponse
    isShowTitle?: boolean
    outModelData?: Record<string, number | symbol | unknown | string | undefined>[] | null
}

const props = withDefaults(defineProps<definetypes>(), {
    isShowTitle: true,
    outModelData: null,
})

const emits = defineEmits(['updateTotal', 'updateBuyStatus'])

const jsonModelCurrentVersion: Ref<IModelJson[]> = ref([])

if (props.modelItem) {
    try {
        
        let jStrList = JSON.parse(props.modelItem.currentVersion.jsonStr)
        jStrList = jStrList.list.filter((r: IModelJson) => !r.options.hidden)
        console.log('jStrList', jStrList)
        jsonModelCurrentVersion.value = jStrList
        console.log('jsonModelCurrentVersion', jsonModelCurrentVersion.value)
    } catch (err) {
        console.log(err)
    }
}

const getCompanyDetail = (entName: string) => {
    aicService
        .searchEnterprise({
            keyword: entName,
            scope: 'companyname',
            pageSize: 1,
            page: 1,
        })
        .then((res) => {
            console.log(res)
            let company = res.data[0]
            if (company.companyName === entName) {
                // const routeUrl = router.resolve({ name: 'company-profile', params: { socialCreditCode: company.socialCreditCode } }).href
                // window.open(routeUrl, '_blank')
                if (window.self === window.top) {
                    const routeUrl = router.resolve({
                        name: 'company-profile',
                        params: { socialCreditCode: company.socialCreditCode },
                    }).href
                    window.open(routeUrl, '_blank')
                } else {
                    router.push({
                        name: 'company-profile',
                        params: {
                            socialCreditCode: company.socialCreditCode,
                        },
                    })
                }
            } else {
                ElMessage({
                    message: '未找到该公司',
                    type: 'error',
                })
            }
        })
        .finally(() => {})
}

const toProjectUrl = (row: {URL: string}) => {
    window.open(row.URL, '_blank')
}

// console.log(props.modelItem.name, jsonModelCurrentVersion.value)

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))

const companyName: Ref<string> = inject('companyName', ref(''))
const modelData: Ref<Record<string, number | symbol | unknown | string | undefined>[] | object[]> = ref([])

// const generateForm = ref(null)

const loading = ref(false)

const moreInfoStr: Ref<string> = ref('') //更多信息的保存字符串

const moreInfoDialogShow: Ref<boolean> = ref(false) //更多信息的弹窗标记位

// const innerTableShow: Ref<boolean> = ref(false) //内部表格的显示标记位

// const innerTableData: Ref<object[]> = ref([]) //内部表格的数据

// const innerTableSchema: Ref<object[]> = ref([]) //内部表格的展示列

const isLock: Ref<boolean> = ref(false)

const channelType: Ref<number> = ref(0)

const jumpHref = () => {
    // location.hash = `#model-${name}`
    document.getElementById(`model-${props.modelItem.name}`)?.scrollIntoView({
        behavior: 'smooth',
        inline: 'nearest',
    })
}
//获取表格数据
const getModelData = () => {
    if (!props.modelItem?.name) {
        return
    }
    loading.value = true
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
        })
        .then((res) => {
            // console.log('data', props.modelItem.name, res)
            isLock.value = res.isLock === 1 ? true : false
            channelType.value = res.channelType
            modelData.value = res.items
            pageInfo.value.total = res.total
            emits('updateTotal', res.total)
            if (pageInfo.value.page !== 1) {
                jumpHref()
            }
        })
        .finally(() => {
            loading.value = false
        })
}

const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})

//计算表格的index
// const indexFilter = (index: number) => {
//     // (当前页-1)*每页显示的条数+table索引+1
//     return (pageInfo.value.page - 1) * pageInfo.value.pageSize + index + 1
// }

//超长字段处理
const toLangStr = (str: string, type: string, pro: IModelJson) => {
    const reg = /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(.\d{3})?Z$/

    let arrReg = /^\[".*/
    if (!str || str === 'null') {
        return { flag: false, str: '-' }
    } else if (type === 'date') {
        return { flag: false, str }
    } else if (str.length > 25 && pro?.options?.customClass !== 'url') {
        return { flag: true, str: str.slice(0, 25) + '...' }
    } else if (type === 'object') {
        return { flag: false, str: str[0] + '-' + str[1] + '-' + str[2] }
    } else if (pro?.options?.customClass === 'percent') {
        return { flag: false, str: Number(str).toFixed(2) + '%' }
    } else if (pro?.options?.customClass === 'url') {
        return {
            flag: false,
            str: `<a href="${str}" target="_blank" style="text-decoration: none; color: #509de5" >查看链接</a>`,
        }
    } else if (arrReg.test(str)) {
        return { flag: false, str: JSON.parse(str).toString() }
    } else if (reg.test(str)) {
        return { flag: false, str }
    } else {
        return { flag: false, str: str }
    }
}

watch(
    () => props.outModelData,
    () => {
        if (props.modelItem && props.modelItem.innerModel) {
            const data = props.outModelData[props.modelItem.innerModel]
            if (Array.isArray(data)) {
                modelData.value = data
            } else {
                modelData.value = []
            }
        } else {
            modelData.value = props.outModelData || []
        }
    },
    { immediate: true }
)

const pullLock = () => {
    ElMessageBox.confirm('扣除对应线索权益额度，是否确定使用?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            orderService
                .orderBuyLegal({
                    socialCreditCode: socialCreditCode.value,
                    companyName: companyName.value,
                    serviceKey: 'xs',
                })
                .then(() => {
                    isLock.value = false
                    emits('updateBuyStatus', true)
                    ElMessage({
                        message: '使用成功',
                        type: 'success',
                    })
                })
        })
        .catch(() => {})
}

interface IAnnualReportParams {
    address: string
    annualReportId: string
    publicshDate: string
    reportYear: string
    socialSecNum: string
}
const annualRow = ref<IAnnualReportParams>()
const annualReportDialogVisible = ref(false)
const handleAnnualReport = (row: IAnnualReportParams) => {
    console.log('handleReportYear', row)
    annualReportDialogVisible.value = true
    annualRow.value = row
    // console.log('annualReportId',annualReportId.value)
}
interface IPatenetDetailParams {
    APPLYDATE: string
    APPLYNO: string
    APPLYPUBDATE: string
    APPLYPUBNO: string
    NOTICETYPE: string
    PATENTNAME: string
    lprs?: string
}
const patentRow = ref<IPatenetDetailParams>()
const patentDetailDialogVisible = ref(false)
const getPatentDetail = (row: IPatenetDetailParams) => {
    console.log('handlePatentDetail', row)
    patentRow.value = row 
    patentDetailDialogVisible.value = true
}
onMounted(() => {
    if (!props.outModelData) {
        getModelData()
    }
})
</script>

<style lang="scss" scoped>
.box-item {
    width: 100px;
}

::v-deep .el-dialog__header {
    padding-bottom: 30px;
}

::v-deep .el-table__body-wrapper {
    font-size: 14px;
}

::v-deep em {
    color: red;
    font-style: normal;
}

.no-pay-item {
    background: url('@/assets/images/model-lock.jpg') no-repeat;
    width: 100%;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    border: 3px solid var(--border-color);
}

.text-box {
    display: -webkit-box;
    /* 使用弹性盒子模型 */
    -webkit-box-orient: vertical;
    /* 设置盒子排列方向为垂直 */
    -webkit-line-clamp: 3;
}

</style>

